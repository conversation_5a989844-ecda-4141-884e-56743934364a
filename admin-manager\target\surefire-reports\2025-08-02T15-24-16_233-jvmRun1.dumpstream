# Created at 2025-08-02T15:24:18.724
Unexpected IOException: I,1,sun.desktop,windows


# Created at 2025-08-02T15:24:18.726
Unexpected IOException: I,1,awt.toolkit,sun.awt.windows.WToolkit


# Created at 2025-08-02T15:24:18.727
Unexpected IOException: I,1,file.encoding.pkg,sun.io


# Created at 2025-08-02T15:24:18.729
Unexpected IOException: I,1,java.specification.version,1.8


# Created at 2025-08-02T15:24:18.730
Unexpected IOException: I,1,sun.cpu.isalist,amd64


# Created at 2025-08-02T15:24:18.731
Unexpected IOException: I,1,sun.jnu.encoding,GBK


# Created at 2025-08-02T15:24:18.741
Unexpected IOException: I,1,java.class.path,D:\005Cyny\005C4g\005Cadmin\005Cadmin-manager\005Ctarget\005Ctest-classes;D:\005Cyny\005C4g\005Cadmin\005Cadmin-manager\005Ctarget\005Cclasses;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-web\005C2.5.2\005Cspring-boot-starter-web-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter\005C2.5.2\005Cspring-boot-starter-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot\005C2.5.2\005Cspring-boot-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-logging\005C2.5.2\005Cspring-boot-starter-logging-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cch\005Cqos\005Clogback\005Clogback-classic\005C1.2.3\005Clogback-classic-1.2.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cch\005Cqos\005Clogback\005Clogback-core\005C1.2.3\005Clogback-core-1.2.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Clogging\005Clog4j\005Clog4j-to-slf4j\005C2.14.1\005Clog4j-to-slf4j-2.14.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Clogging\005Clog4j\005Clog4j-api\005C2.14.1\005Clog4j-api-2.14.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cjul-to-slf4j\005C1.7.31\005Cjul-to-slf4j-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cannotation\005Cjakarta.annotation-api\005C1.3.5\005Cjakarta.annotation-api-1.3.5.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cyaml\005Csnakeyaml\005C1.28\005Csnakeyaml-1.28.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-json\005C2.5.2\005Cspring-boot-starter-json-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Cdatatype\005Cjackson-datatype-jdk8\005C2.12.3\005Cjackson-datatype-jdk8-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Cdatatype\005Cjackson-datatype-jsr310\005C2.12.3\005Cjackson-datatype-jsr310-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Cmodule\005Cjackson-module-parameter-names\005C2.12.3\005Cjackson-module-parameter-names-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-tomcat\005C2.5.2\005Cspring-boot-starter-tomcat-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ctomcat\005Cembed\005Ctomcat-embed-core\005C9.0.48\005Ctomcat-embed-core-9.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ctomcat\005Cembed\005Ctomcat-embed-websocket\005C9.0.48\005Ctomcat-embed-websocket-9.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-web\005C5.3.8\005Cspring-web-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-beans\005C5.3.8\005Cspring-beans-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-webmvc\005C5.3.8\005Cspring-webmvc-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-context\005C5.3.8\005Cspring-context-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-expression\005C5.3.8\005Cspring-expression-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-validation\005C2.5.2\005Cspring-boot-starter-validation-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ctomcat\005Cembed\005Ctomcat-embed-el\005C9.0.48\005Ctomcat-embed-el-9.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chibernate\005Cvalidator\005Chibernate-validator\005C6.2.0.Final\005Chibernate-validator-6.2.0.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cvalidation\005Cjakarta.validation-api\005C2.0.2\005Cjakarta.validation-api-2.0.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjboss\005Clogging\005Cjboss-logging\005C3.4.2.Final\005Cjboss-logging-3.4.2.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cclassmate\005C1.5.1\005Cclassmate-1.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-data-jpa\005C2.5.2\005Cspring-boot-starter-data-jpa-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-jdbc\005C2.5.2\005Cspring-boot-starter-jdbc-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Czaxxer\005CHikariCP\005C4.0.3\005CHikariCP-4.0.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-jdbc\005C5.3.8\005Cspring-jdbc-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Ctransaction\005Cjakarta.transaction-api\005C1.3.3\005Cjakarta.transaction-api-1.3.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cpersistence\005Cjakarta.persistence-api\005C2.2.3\005Cjakarta.persistence-api-2.2.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chibernate\005Chibernate-core\005C5.4.32.Final\005Chibernate-core-5.4.32.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjavassist\005Cjavassist\005C3.27.0-GA\005Cjavassist-3.27.0-GA.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cbytebuddy\005Cbyte-buddy\005C1.10.22\005Cbyte-buddy-1.10.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cantlr\005Cantlr\005C2.7.7\005Cantlr-2.7.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjboss\005Cjandex\005C2.2.3.Final\005Cjandex-2.2.3.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cdom4j\005Cdom4j\005C2.1.3\005Cdom4j-2.1.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chibernate\005Ccommon\005Chibernate-commons-annotations\005C5.1.2.Final\005Chibernate-commons-annotations-5.1.2.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cglassfish\005Cjaxb\005Cjaxb-runtime\005C2.3.4\005Cjaxb-runtime-2.3.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cglassfish\005Cjaxb\005Ctxw2\005C2.3.4\005Ctxw2-2.3.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cistack\005Cistack-commons-runtime\005C3.0.12\005Cistack-commons-runtime-3.0.12.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cactivation\005Cjakarta.activation\005C1.2.2\005Cjakarta.activation-1.2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-jpa\005C2.5.2\005Cspring-data-jpa-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-commons\005C2.5.2\005Cspring-data-commons-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-orm\005C5.3.8\005Cspring-orm-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-tx\005C5.3.8\005Cspring-tx-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-aspects\005C5.3.8\005Cspring-aspects-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-security\005C2.5.2\005Cspring-boot-starter-security-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-aop\005C5.3.8\005Cspring-aop-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-config\005C5.5.1\005Cspring-security-config-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-web\005C5.5.1\005Cspring-security-web-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-test\005C5.5.1\005Cspring-security-test-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-core\005C5.5.1\005Cspring-security-core-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-crypto\005C5.5.1\005Cspring-security-crypto-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-core\005C5.3.8\005Cspring-core-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-jcl\005C5.3.8\005Cspring-jcl-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-test\005C5.3.8\005Cspring-test-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cjsonwebtoken\005Cjjwt\005C0.9.1\005Cjjwt-0.9.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Ccore\005Cjackson-databind\005C2.12.3\005Cjackson-databind-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-aop\005C2.5.2\005Cspring-boot-starter-aop-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Caspectj\005Caspectjweaver\005C1.9.6\005Caspectjweaver-1.9.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-actuator\005C2.5.2\005Cspring-boot-starter-actuator-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-actuator-autoconfigure\005C2.5.2\005Cspring-boot-actuator-autoconfigure-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-actuator\005C2.5.2\005Cspring-boot-actuator-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cmicrometer\005Cmicrometer-core\005C1.7.1\005Cmicrometer-core-1.7.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chdrhistogram\005CHdrHistogram\005C2.1.12\005CHdrHistogram-2.1.12.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Clatencyutils\005CLatencyUtils\005C2.0.3\005CLatencyUtils-2.0.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cintegration\005Cspring-integration-mqtt\005C5.5.1\005Cspring-integration-mqtt-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cintegration\005Cspring-integration-core\005C5.5.1\005Cspring-integration-core-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-messaging\005C5.3.8\005Cspring-messaging-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cretry\005Cspring-retry\005C1.3.1\005Cspring-retry-1.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ceclipse\005Cpaho\005Corg.eclipse.paho.client.mqttv3\005C1.2.5\005Corg.eclipse.paho.client.mqttv3-1.2.5.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-apt\005C4.4.0\005Cquerydsl-apt-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-codegen\005C4.4.0\005Cquerydsl-codegen-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cmysema\005Ccodegen\005Ccodegen\005C0.6.8\005Ccodegen-0.6.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ceclipse\005Cjdt\005Ccore\005Ccompiler\005Cecj\005C4.3.1\005Cecj-4.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Creflections\005Creflections\005C0.9.9\005Creflections-0.9.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Ccode\005Cfindbugs\005Cannotations\005C2.0.1\005Cannotations-2.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-jpa\005C4.4.0\005Cquerydsl-jpa-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-core\005C4.4.0\005Cquerydsl-core-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Cguava\005Cguava\005C18.0\005Cguava-18.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Ccode\005Cfindbugs\005Cjsr305\005C1.3.9\005Cjsr305-1.3.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cmysema\005Ccommons\005Cmysema-commons-lang\005C0.2.4\005Cmysema-commons-lang-0.2.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cinfradna\005Ctool\005Cbridge-method-annotation\005C1.13\005Cbridge-method-annotation-1.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cinject\005Cjavax.inject\005C1\005Cjavax.inject-1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cslf4j-api\005C1.7.31\005Cslf4j-api-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-configuration-processor\005C2.5.2\005Cspring-boot-configuration-processor-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cmysql\005Cmysql-connector-java\005C8.0.25\005Cmysql-connector-java-8.0.25.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cdruid-spring-boot-starter\005C1.1.22\005Cdruid-spring-boot-starter-1.1.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cdruid\005C1.1.22\005Cdruid-1.1.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-autoconfigure\005C2.5.2\005Cspring-boot-autoconfigure-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Credis\005Cclients\005Cjedis\005C3.6.1\005Cjedis-3.6.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ccommons\005Ccommons-pool2\005C2.9.0\005Ccommons-pool2-2.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ccommons\005Ccommons-lang3\005C3.12.0\005Ccommons-lang3-3.12.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccommons-io\005Ccommons-io\005C2.7\005Ccommons-io-2.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cfastjson\005C1.2.73\005Cfastjson-1.2.73.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccn\005Chutool\005Chutool-all\005C5.4.3\005Chutool-all-5.4.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccglib\005Ccglib\005C3.3.0\005Ccglib-3.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cow2\005Casm\005Casm\005C7.1\005Casm-7.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ceu\005Cbitwalker\005CUserAgentUtils\005C1.21\005CUserAgentUtils-1.21.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cpenggle\005Ckaptcha\005C2.3.2\005Ckaptcha-2.3.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cjhlabs\005Cfilters\005C2.0.235-1\005Cfilters-2.0.235-1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Ceasyexcel\005C2.2.10\005Ceasyexcel-2.2.10.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cpoi\005Cpoi\005C3.17\005Cpoi-3.17.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ccommons\005Ccommons-collections4\005C4.1\005Ccommons-collections4-4.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cpoi\005Cpoi-ooxml\005C3.17\005Cpoi-ooxml-3.17.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cvirtuald\005Ccurvesapi\005C1.04\005Ccurvesapi-1.04.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cpoi\005Cpoi-ooxml-schemas\005C3.17\005Cpoi-ooxml-schemas-3.17.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cxmlbeans\005Cxmlbeans\005C2.6.0\005Cxmlbeans-2.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cehcache\005Cehcache\005C3.9.4\005Cehcache-3.9.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Coss\005Caliyun-sdk-oss\005C3.11.1\005Caliyun-sdk-oss-3.11.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpclient\005C4.5.13\005Chttpclient-4.5.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpcore\005C4.4.14\005Chttpcore-4.4.14.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjdom\005Cjdom2\005C2.0.6\005Cjdom2-2.0.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ccodehaus\005Cjettison\005Cjettison\005C1.1\005Cjettison-1.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cstax\005Cstax-api\005C1.0.1\005Cstax-api-1.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Caliyun-java-sdk-core\005C4.5.10\005Caliyun-java-sdk-core-4.5.10.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccommons-logging\005Ccommons-logging\005C1.2\005Ccommons-logging-1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cxml\005Cbind\005Cjaxb-api\005C2.3.1\005Cjaxb-api-2.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cactivation\005Cjavax.activation-api\005C1.2.0\005Cjavax.activation-api-1.2.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjacoco\005Corg.jacoco.agent\005C0.8.5\005Corg.jacoco.agent-0.8.5-runtime.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cini4j\005Cini4j\005C0.5.4\005Cini4j-0.5.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Copentracing\005Copentracing-api\005C0.33.0\005Copentracing-api-0.33.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Copentracing\005Copentracing-util\005C0.33.0\005Copentracing-util-0.33.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Copentracing\005Copentracing-noop\005C0.33.0\005Copentracing-noop-0.33.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Caliyun-java-sdk-ram\005C3.1.0\005Caliyun-java-sdk-ram-3.1.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Caliyun-java-sdk-kms\005C2.11.0\005Caliyun-java-sdk-kms-2.11.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-test\005C2.5.2\005Cspring-boot-starter-test-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-test\005C2.5.2\005Cspring-boot-test-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-test-autoconfigure\005C2.5.2\005Cspring-boot-test-autoconfigure-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cjayway\005Cjsonpath\005Cjson-path\005C2.5.0\005Cjson-path-2.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cminidev\005Cjson-smart\005C2.4.7\005Cjson-smart-2.4.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cminidev\005Caccessors-smart\005C2.4.7\005Caccessors-smart-2.4.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cxml\005Cbind\005Cjakarta.xml.bind-api\005C2.3.3\005Cjakarta.xml.bind-api-2.3.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cactivation\005Cjakarta.activation-api\005C1.2.2\005Cjakarta.activation-api-1.2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cassertj\005Cassertj-core\005C3.19.0\005Cassertj-core-3.19.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chamcrest\005Chamcrest\005C2.2\005Chamcrest-2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter\005C5.7.2\005Cjunit-jupiter-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter-api\005C5.7.2\005Cjunit-jupiter-api-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capiguardian\005Capiguardian-api\005C1.1.0\005Capiguardian-api-1.1.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copentest4j\005Copentest4j\005C1.2.0\005Copentest4j-1.2.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cplatform\005Cjunit-platform-commons\005C1.7.2\005Cjunit-platform-commons-1.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter-params\005C5.7.2\005Cjunit-jupiter-params-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter-engine\005C5.7.2\005Cjunit-jupiter-engine-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cplatform\005Cjunit-platform-engine\005C1.7.2\005Cjunit-platform-engine-1.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmockito\005Cmockito-core\005C3.9.0\005Cmockito-core-3.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cbytebuddy\005Cbyte-buddy-agent\005C1.10.22\005Cbyte-buddy-agent-1.10.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cobjenesis\005Cobjenesis\005C3.2\005Cobjenesis-3.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmockito\005Cmockito-junit-jupiter\005C3.9.0\005Cmockito-junit-jupiter-3.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cskyscreamer\005Cjsonassert\005C1.5.0\005Cjsonassert-1.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cvaadin\005Cexternal\005Cgoogle\005Candroid-json\005C0.0.20131108.vaadin1\005Candroid-json-0.0.20131108.vaadin1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cxmlunit\005Cxmlunit-core\005C2.8.2\005Cxmlunit-core-2.8.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmockito\005Cmockito-inline\005C3.9.0\005Cmockito-inline-3.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Ch2database\005Ch2\005C1.4.200\005Ch2-1.4.200.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cprojectlombok\005Clombok\005C1.18.20\005Clombok-1.18.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjaudiotagger\005C2.0.1\005Cjaudiotagger-2.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cweixin-java-pay\005C4.5.0\005Cweixin-java-pay-4.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cweixin-java-common\005C4.5.0\005Cweixin-java-common-4.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cthoughtworks\005Cxstream\005Cxstream\005C1.4.20\005Cxstream-1.4.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cgithub\005Cx-stream\005Cmxparser\005C1.2.2\005Cmxparser-1.2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cxmlpull\005Cxmlpull\005C1.1.3.1\005Cxmlpull-1.1.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpmime\005C4.5.13\005Chttpmime-4.5.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cjcl-over-slf4j\005C1.7.31\005Cjcl-over-slf4j-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cqrcode-utils\005C1.1\005Cqrcode-utils-1.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Czxing\005Ccore\005C3.2.1\005Ccore-3.2.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cbouncycastle\005Cbcpkix-jdk15on\005C1.68\005Cbcpkix-jdk15on-1.68.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cbouncycastle\005Cbcprov-jdk15on\005C1.68\005Cbcprov-jdk15on-1.68.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Ccode\005Cgson\005Cgson\005C2.8.7\005Cgson-2.8.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cservlet\005Cjavax.servlet-api\005C4.0.1\005Cjavax.servlet-api-4.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cweixin-java-mp\005C4.4.0\005Cweixin-java-mp-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgooglecode\005Cmp4parser\005Cisoparser\005C1.1.22\005Cisoparser-1.1.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ccodehaus\005Cjackson\005Cjackson-mapper-asl\005C1.9.13\005Cjackson-mapper-asl-1.9.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ccodehaus\005Cjackson\005Cjackson-core-asl\005C1.9.13\005Cjackson-core-asl-1.9.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cit\005Csauronsoftware\005Cjave\005C1.0.2\005Cjave-1.0.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cmicrosoft\005Ccognitiveservices\005Cspeech\005Cclient-sdk\005C1.40.0\005Cclient-sdk-1.40.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Chuaweicloud\005Csdk\005Chuaweicloud-sdk-sis\005C3.1.112\005Chuaweicloud-sdk-sis-3.1.112.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Chuaweicloud\005Csdk\005Chuaweicloud-sdk-core\005C3.1.112\005Chuaweicloud-sdk-core-3.1.112.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copeneuler\005Cbgmprovider\005C1.1.2\005Cbgmprovider-1.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copeneuler\005Cjca\005C1.1.2\005Cjca-1.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cbouncycastle\005Cbcprov-jdk18on\005C1.75\005Cbcprov-jdk18on-1.75.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copeneuler\005Cjsse\005C1.1.2\005Cjsse-1.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Ctencentcloudapi\005Ctencentcloud-speech-sdk-java\005C1.0.48\005Ctencentcloud-speech-sdk-java-1.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccn\005Chutool\005Chutool-core\005C5.8.11\005Chutool-core-5.8.11.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccommons-codec\005Ccommons-codec\005C1.15\005Ccommons-codec-1.15.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokhttp3\005Cokhttp\005C3.14.9\005Cokhttp-3.14.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokhttp3\005Clogging-interceptor\005C3.14.9\005Clogging-interceptor-3.14.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpasyncclient\005C4.1.4\005Chttpasyncclient-4.1.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpcore-nio\005C4.4.14\005Chttpcore-nio-4.4.14.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cjodah\005Cexpiringmap\005C0.5.8\005Cexpiringmap-0.5.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Ccore\005Cjackson-annotations\005C2.12.3\005Cjackson-annotations-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Ccore\005Cjackson-core\005C2.12.3\005Cjackson-core-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cdashscope-sdk-java\005C2.18.0\005Cdashscope-sdk-java-2.18.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Creactivex\005Crxjava2\005Crxjava\005C2.2.21\005Crxjava-2.2.21.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Creactivestreams\005Creactive-streams\005C1.0.3\005Creactive-streams-1.0.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cslf4j-simple\005C1.7.31\005Cslf4j-simple-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib-jdk8\005C1.5.20\005Ckotlin-stdlib-jdk8-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib\005C1.5.20\005Ckotlin-stdlib-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib-common\005C1.5.20\005Ckotlin-stdlib-common-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib-jdk7\005C1.5.20\005Ckotlin-stdlib-jdk7-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokio\005Cokio\005C3.6.0\005Cokio-3.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokio\005Cokio-jvm\005C3.6.0\005Cokio-jvm-3.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokhttp3\005Cokhttp-sse\005C3.14.9\005Cokhttp-sse-3.14.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cvictools\005Cjsonschema-generator\005C4.31.1\005Cjsonschema-generator-4.31.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Cbroadscope-bailian-sdk-java\005C1.3.0\005Cbroadscope-bailian-sdk-java-1.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Cbailian20230601\005C1.7.0\005Cbailian20230601-1.7.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea-util\005C0.2.21\005Ctea-util-0.2.21.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea-openapi\005C0.3.1\005Ctea-openapi-0.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ccredentials-java\005C0.3.0\005Ccredentials-java-0.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cxml\005Cbind\005Cjaxb-core\005C2.3.0\005Cjaxb-core-2.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cxml\005Cbind\005Cjaxb-impl\005C2.3.0\005Cjaxb-impl-2.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Calibabacloud-gateway-spi\005C0.0.1\005Calibabacloud-gateway-spi-0.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea-xml\005C0.1.5\005Ctea-xml-0.1.5.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Copenapiutil\005C0.2.1\005Copenapiutil-0.2.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Cendpoint-util\005C0.0.7\005Cendpoint-util-0.0.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea\005C1.2.7\005Ctea-1.2.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Cannotations\005C23.0.0\005Cannotations-23.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cprojectreactor\005Creactor-core\005C3.4.7\005Creactor-core-3.4.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-all\005C4.1.25.Final\005Cnetty-all-4.1.25.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgl\005Cadmin\005Cadmin-core\005C1.6.0\005Cadmin-core-1.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-data-redis\005C2.5.2\005Cspring-boot-starter-data-redis-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-redis\005C2.5.2\005Cspring-data-redis-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-keyvalue\005C2.5.2\005Cspring-data-keyvalue-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-oxm\005C5.3.8\005Cspring-oxm-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-context-support\005C5.3.8\005Cspring-context-support-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-boot-starter\005C3.0.0\005Cspringfox-boot-starter-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-oas\005C3.0.0\005Cspringfox-oas-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Ccore\005Cv3\005Cswagger-annotations\005C2.1.2\005Cswagger-annotations-2.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Ccore\005Cv3\005Cswagger-models\005C2.1.2\005Cswagger-models-2.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spi\005C3.0.0\005Cspringfox-spi-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-schema\005C3.0.0\005Cspringfox-schema-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-core\005C3.0.0\005Cspringfox-core-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spring-web\005C3.0.0\005Cspringfox-spring-web-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cgithub\005Cclassgraph\005Cclassgraph\005C4.8.83\005Cclassgraph-4.8.83.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spring-webmvc\005C3.0.0\005Cspringfox-spring-webmvc-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spring-webflux\005C3.0.0\005Cspringfox-spring-webflux-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-swagger-common\005C3.0.0\005Cspringfox-swagger-common-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmapstruct\005Cmapstruct\005C1.3.1.Final\005Cmapstruct-1.3.1.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-data-rest\005C3.0.0\005Cspringfox-data-rest-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-bean-validators\005C3.0.0\005Cspringfox-bean-validators-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-swagger2\005C3.0.0\005Cspringfox-swagger2-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Cswagger-annotations\005C1.5.20\005Cswagger-annotations-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Cswagger-models\005C1.5.20\005Cswagger-models-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-swagger-ui\005C3.0.0\005Cspringfox-swagger-ui-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cplugin\005Cspring-plugin-core\005C2.0.0.RELEASE\005Cspring-plugin-core-2.0.0.RELEASE.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cplugin\005Cspring-plugin-metadata\005C2.0.0.RELEASE\005Cspring-plugin-metadata-2.0.0.RELEASE.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cfreemarker\005Cfreemarker\005C2.3.31\005Cfreemarker-2.3.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjdom\005Cjdom\005C1.1.3\005Cjdom-1.1.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgl\005Cadmin\005Cadmin-commons\005C1.6.0\005Cadmin-commons-1.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cjamesmurty\005Cutils\005Cjava-xmlbuilder\005C1.3\005Cjava-xmlbuilder-1.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cnls\005Cnls-sdk-tts\005C2.1.6\005Cnls-sdk-tts-2.1.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cnls\005Cnls-sdk-common\005C2.1.6\005Cnls-sdk-common-2.1.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-transport\005C4.1.65.Final\005Cnetty-transport-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-resolver\005C4.1.65.Final\005Cnetty-resolver-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-handler\005C4.1.65.Final\005Cnetty-handler-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-common\005C4.1.65.Final\005Cnetty-common-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-codec-http\005C4.1.65.Final\005Cnetty-codec-http-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-codec\005C4.1.65.Final\005Cnetty-codec-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-buffer\005C4.1.65.Final\005Cnetty-buffer-4.1.65.Final.jar;


# Created at 2025-08-02T15:24:18.743
Unexpected IOException: I,1,java.vm.vendor,Azul Systems\002C Inc.


# Created at 2025-08-02T15:24:18.744
Unexpected IOException: I,1,sun.arch.data.model,64


# Created at 2025-08-02T15:24:18.745
Unexpected IOException: I,1,user.variant,


# Created at 2025-08-02T15:24:18.746
Unexpected IOException: I,1,java.vendor.url,http://www.azul.com/


# Created at 2025-08-02T15:24:18.747
Unexpected IOException: I,1,user.timezone,Asia/Shanghai


# Created at 2025-08-02T15:24:18.751
Unexpected IOException: I,1,os.name,Windows 11


# Created at 2025-08-02T15:24:18.753
Unexpected IOException: I,1,java.vm.specification.version,1.8


# Created at 2025-08-02T15:24:18.754
Unexpected IOException: I,1,user.country,CN


# Created at 2025-08-02T15:24:18.755
Unexpected IOException: I,1,sun.java.launcher,SUN_STANDARD


# Created at 2025-08-02T15:24:18.757
Unexpected IOException: I,1,sun.boot.library.path,C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Cbin


# Created at 2025-08-02T15:24:18.758
Unexpected IOException: I,1,sun.java.command,C:\005CUsers\005CADMINI~1\005CAppData\005CLocal\005CTemp\005Csurefire295056551933935664\005Csurefirebooter6908574142842254977.jar C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CTemp\005Csurefire295056551933935664 2025-08-02T15-24-16_233-jvmRun1 surefire222894893914621692tmp surefire_01432024916793491057tmp


# Created at 2025-08-02T15:24:18.758
Unexpected IOException: I,1,test,TemplateControllerTest#testList_Success


# Created at 2025-08-02T15:24:18.765
Unexpected IOException: I,1,surefire.test.class.path,D:\005Cyny\005C4g\005Cadmin\005Cadmin-manager\005Ctarget\005Ctest-classes;D:\005Cyny\005C4g\005Cadmin\005Cadmin-manager\005Ctarget\005Cclasses;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-web\005C2.5.2\005Cspring-boot-starter-web-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter\005C2.5.2\005Cspring-boot-starter-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot\005C2.5.2\005Cspring-boot-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-logging\005C2.5.2\005Cspring-boot-starter-logging-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cch\005Cqos\005Clogback\005Clogback-classic\005C1.2.3\005Clogback-classic-1.2.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cch\005Cqos\005Clogback\005Clogback-core\005C1.2.3\005Clogback-core-1.2.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Clogging\005Clog4j\005Clog4j-to-slf4j\005C2.14.1\005Clog4j-to-slf4j-2.14.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Clogging\005Clog4j\005Clog4j-api\005C2.14.1\005Clog4j-api-2.14.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cjul-to-slf4j\005C1.7.31\005Cjul-to-slf4j-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cannotation\005Cjakarta.annotation-api\005C1.3.5\005Cjakarta.annotation-api-1.3.5.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cyaml\005Csnakeyaml\005C1.28\005Csnakeyaml-1.28.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-json\005C2.5.2\005Cspring-boot-starter-json-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Cdatatype\005Cjackson-datatype-jdk8\005C2.12.3\005Cjackson-datatype-jdk8-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Cdatatype\005Cjackson-datatype-jsr310\005C2.12.3\005Cjackson-datatype-jsr310-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Cmodule\005Cjackson-module-parameter-names\005C2.12.3\005Cjackson-module-parameter-names-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-tomcat\005C2.5.2\005Cspring-boot-starter-tomcat-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ctomcat\005Cembed\005Ctomcat-embed-core\005C9.0.48\005Ctomcat-embed-core-9.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ctomcat\005Cembed\005Ctomcat-embed-websocket\005C9.0.48\005Ctomcat-embed-websocket-9.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-web\005C5.3.8\005Cspring-web-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-beans\005C5.3.8\005Cspring-beans-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-webmvc\005C5.3.8\005Cspring-webmvc-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-context\005C5.3.8\005Cspring-context-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-expression\005C5.3.8\005Cspring-expression-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-validation\005C2.5.2\005Cspring-boot-starter-validation-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ctomcat\005Cembed\005Ctomcat-embed-el\005C9.0.48\005Ctomcat-embed-el-9.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chibernate\005Cvalidator\005Chibernate-validator\005C6.2.0.Final\005Chibernate-validator-6.2.0.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cvalidation\005Cjakarta.validation-api\005C2.0.2\005Cjakarta.validation-api-2.0.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjboss\005Clogging\005Cjboss-logging\005C3.4.2.Final\005Cjboss-logging-3.4.2.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cclassmate\005C1.5.1\005Cclassmate-1.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-data-jpa\005C2.5.2\005Cspring-boot-starter-data-jpa-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-jdbc\005C2.5.2\005Cspring-boot-starter-jdbc-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Czaxxer\005CHikariCP\005C4.0.3\005CHikariCP-4.0.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-jdbc\005C5.3.8\005Cspring-jdbc-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Ctransaction\005Cjakarta.transaction-api\005C1.3.3\005Cjakarta.transaction-api-1.3.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cpersistence\005Cjakarta.persistence-api\005C2.2.3\005Cjakarta.persistence-api-2.2.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chibernate\005Chibernate-core\005C5.4.32.Final\005Chibernate-core-5.4.32.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjavassist\005Cjavassist\005C3.27.0-GA\005Cjavassist-3.27.0-GA.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cbytebuddy\005Cbyte-buddy\005C1.10.22\005Cbyte-buddy-1.10.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cantlr\005Cantlr\005C2.7.7\005Cantlr-2.7.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjboss\005Cjandex\005C2.2.3.Final\005Cjandex-2.2.3.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cdom4j\005Cdom4j\005C2.1.3\005Cdom4j-2.1.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chibernate\005Ccommon\005Chibernate-commons-annotations\005C5.1.2.Final\005Chibernate-commons-annotations-5.1.2.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cglassfish\005Cjaxb\005Cjaxb-runtime\005C2.3.4\005Cjaxb-runtime-2.3.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cglassfish\005Cjaxb\005Ctxw2\005C2.3.4\005Ctxw2-2.3.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cistack\005Cistack-commons-runtime\005C3.0.12\005Cistack-commons-runtime-3.0.12.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cactivation\005Cjakarta.activation\005C1.2.2\005Cjakarta.activation-1.2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-jpa\005C2.5.2\005Cspring-data-jpa-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-commons\005C2.5.2\005Cspring-data-commons-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-orm\005C5.3.8\005Cspring-orm-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-tx\005C5.3.8\005Cspring-tx-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-aspects\005C5.3.8\005Cspring-aspects-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-security\005C2.5.2\005Cspring-boot-starter-security-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-aop\005C5.3.8\005Cspring-aop-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-config\005C5.5.1\005Cspring-security-config-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-web\005C5.5.1\005Cspring-security-web-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-test\005C5.5.1\005Cspring-security-test-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-core\005C5.5.1\005Cspring-security-core-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Csecurity\005Cspring-security-crypto\005C5.5.1\005Cspring-security-crypto-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-core\005C5.3.8\005Cspring-core-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-jcl\005C5.3.8\005Cspring-jcl-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-test\005C5.3.8\005Cspring-test-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cjsonwebtoken\005Cjjwt\005C0.9.1\005Cjjwt-0.9.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Ccore\005Cjackson-databind\005C2.12.3\005Cjackson-databind-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-aop\005C2.5.2\005Cspring-boot-starter-aop-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Caspectj\005Caspectjweaver\005C1.9.6\005Caspectjweaver-1.9.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-actuator\005C2.5.2\005Cspring-boot-starter-actuator-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-actuator-autoconfigure\005C2.5.2\005Cspring-boot-actuator-autoconfigure-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-actuator\005C2.5.2\005Cspring-boot-actuator-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cmicrometer\005Cmicrometer-core\005C1.7.1\005Cmicrometer-core-1.7.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chdrhistogram\005CHdrHistogram\005C2.1.12\005CHdrHistogram-2.1.12.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Clatencyutils\005CLatencyUtils\005C2.0.3\005CLatencyUtils-2.0.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cintegration\005Cspring-integration-mqtt\005C5.5.1\005Cspring-integration-mqtt-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cintegration\005Cspring-integration-core\005C5.5.1\005Cspring-integration-core-5.5.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-messaging\005C5.3.8\005Cspring-messaging-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cretry\005Cspring-retry\005C1.3.1\005Cspring-retry-1.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ceclipse\005Cpaho\005Corg.eclipse.paho.client.mqttv3\005C1.2.5\005Corg.eclipse.paho.client.mqttv3-1.2.5.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-apt\005C4.4.0\005Cquerydsl-apt-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-codegen\005C4.4.0\005Cquerydsl-codegen-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cmysema\005Ccodegen\005Ccodegen\005C0.6.8\005Ccodegen-0.6.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ceclipse\005Cjdt\005Ccore\005Ccompiler\005Cecj\005C4.3.1\005Cecj-4.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Creflections\005Creflections\005C0.9.9\005Creflections-0.9.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Ccode\005Cfindbugs\005Cannotations\005C2.0.1\005Cannotations-2.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-jpa\005C4.4.0\005Cquerydsl-jpa-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cquerydsl\005Cquerydsl-core\005C4.4.0\005Cquerydsl-core-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Cguava\005Cguava\005C18.0\005Cguava-18.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Ccode\005Cfindbugs\005Cjsr305\005C1.3.9\005Cjsr305-1.3.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cmysema\005Ccommons\005Cmysema-commons-lang\005C0.2.4\005Cmysema-commons-lang-0.2.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cinfradna\005Ctool\005Cbridge-method-annotation\005C1.13\005Cbridge-method-annotation-1.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cinject\005Cjavax.inject\005C1\005Cjavax.inject-1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cslf4j-api\005C1.7.31\005Cslf4j-api-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-configuration-processor\005C2.5.2\005Cspring-boot-configuration-processor-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cmysql\005Cmysql-connector-java\005C8.0.25\005Cmysql-connector-java-8.0.25.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cdruid-spring-boot-starter\005C1.1.22\005Cdruid-spring-boot-starter-1.1.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cdruid\005C1.1.22\005Cdruid-1.1.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-autoconfigure\005C2.5.2\005Cspring-boot-autoconfigure-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Credis\005Cclients\005Cjedis\005C3.6.1\005Cjedis-3.6.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ccommons\005Ccommons-pool2\005C2.9.0\005Ccommons-pool2-2.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ccommons\005Ccommons-lang3\005C3.12.0\005Ccommons-lang3-3.12.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccommons-io\005Ccommons-io\005C2.7\005Ccommons-io-2.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cfastjson\005C1.2.73\005Cfastjson-1.2.73.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccn\005Chutool\005Chutool-all\005C5.4.3\005Chutool-all-5.4.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccglib\005Ccglib\005C3.3.0\005Ccglib-3.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cow2\005Casm\005Casm\005C7.1\005Casm-7.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ceu\005Cbitwalker\005CUserAgentUtils\005C1.21\005CUserAgentUtils-1.21.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cpenggle\005Ckaptcha\005C2.3.2\005Ckaptcha-2.3.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cjhlabs\005Cfilters\005C2.0.235-1\005Cfilters-2.0.235-1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Ceasyexcel\005C2.2.10\005Ceasyexcel-2.2.10.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cpoi\005Cpoi\005C3.17\005Cpoi-3.17.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Ccommons\005Ccommons-collections4\005C4.1\005Ccommons-collections4-4.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cpoi\005Cpoi-ooxml\005C3.17\005Cpoi-ooxml-3.17.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cvirtuald\005Ccurvesapi\005C1.04\005Ccurvesapi-1.04.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cpoi\005Cpoi-ooxml-schemas\005C3.17\005Cpoi-ooxml-schemas-3.17.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Cxmlbeans\005Cxmlbeans\005C2.6.0\005Cxmlbeans-2.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cehcache\005Cehcache\005C3.9.4\005Cehcache-3.9.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Coss\005Caliyun-sdk-oss\005C3.11.1\005Caliyun-sdk-oss-3.11.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpclient\005C4.5.13\005Chttpclient-4.5.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpcore\005C4.4.14\005Chttpcore-4.4.14.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjdom\005Cjdom2\005C2.0.6\005Cjdom2-2.0.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ccodehaus\005Cjettison\005Cjettison\005C1.1\005Cjettison-1.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cstax\005Cstax-api\005C1.0.1\005Cstax-api-1.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Caliyun-java-sdk-core\005C4.5.10\005Caliyun-java-sdk-core-4.5.10.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccommons-logging\005Ccommons-logging\005C1.2\005Ccommons-logging-1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cxml\005Cbind\005Cjaxb-api\005C2.3.1\005Cjaxb-api-2.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cactivation\005Cjavax.activation-api\005C1.2.0\005Cjavax.activation-api-1.2.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjacoco\005Corg.jacoco.agent\005C0.8.5\005Corg.jacoco.agent-0.8.5-runtime.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cini4j\005Cini4j\005C0.5.4\005Cini4j-0.5.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Copentracing\005Copentracing-api\005C0.33.0\005Copentracing-api-0.33.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Copentracing\005Copentracing-util\005C0.33.0\005Copentracing-util-0.33.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Copentracing\005Copentracing-noop\005C0.33.0\005Copentracing-noop-0.33.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Caliyun-java-sdk-ram\005C3.1.0\005Caliyun-java-sdk-ram-3.1.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Caliyun-java-sdk-kms\005C2.11.0\005Caliyun-java-sdk-kms-2.11.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-test\005C2.5.2\005Cspring-boot-starter-test-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-test\005C2.5.2\005Cspring-boot-test-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-test-autoconfigure\005C2.5.2\005Cspring-boot-test-autoconfigure-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cjayway\005Cjsonpath\005Cjson-path\005C2.5.0\005Cjson-path-2.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cminidev\005Cjson-smart\005C2.4.7\005Cjson-smart-2.4.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cminidev\005Caccessors-smart\005C2.4.7\005Caccessors-smart-2.4.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cxml\005Cbind\005Cjakarta.xml.bind-api\005C2.3.3\005Cjakarta.xml.bind-api-2.3.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjakarta\005Cactivation\005Cjakarta.activation-api\005C1.2.2\005Cjakarta.activation-api-1.2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cassertj\005Cassertj-core\005C3.19.0\005Cassertj-core-3.19.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Chamcrest\005Chamcrest\005C2.2\005Chamcrest-2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter\005C5.7.2\005Cjunit-jupiter-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter-api\005C5.7.2\005Cjunit-jupiter-api-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capiguardian\005Capiguardian-api\005C1.1.0\005Capiguardian-api-1.1.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copentest4j\005Copentest4j\005C1.2.0\005Copentest4j-1.2.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cplatform\005Cjunit-platform-commons\005C1.7.2\005Cjunit-platform-commons-1.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter-params\005C5.7.2\005Cjunit-jupiter-params-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cjupiter\005Cjunit-jupiter-engine\005C5.7.2\005Cjunit-jupiter-engine-5.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjunit\005Cplatform\005Cjunit-platform-engine\005C1.7.2\005Cjunit-platform-engine-1.7.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmockito\005Cmockito-core\005C3.9.0\005Cmockito-core-3.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cbytebuddy\005Cbyte-buddy-agent\005C1.10.22\005Cbyte-buddy-agent-1.10.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cobjenesis\005Cobjenesis\005C3.2\005Cobjenesis-3.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmockito\005Cmockito-junit-jupiter\005C3.9.0\005Cmockito-junit-jupiter-3.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cskyscreamer\005Cjsonassert\005C1.5.0\005Cjsonassert-1.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cvaadin\005Cexternal\005Cgoogle\005Candroid-json\005C0.0.20131108.vaadin1\005Candroid-json-0.0.20131108.vaadin1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cxmlunit\005Cxmlunit-core\005C2.8.2\005Cxmlunit-core-2.8.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmockito\005Cmockito-inline\005C3.9.0\005Cmockito-inline-3.9.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Ch2database\005Ch2\005C1.4.200\005Ch2-1.4.200.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cprojectlombok\005Clombok\005C1.18.20\005Clombok-1.18.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjaudiotagger\005C2.0.1\005Cjaudiotagger-2.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cweixin-java-pay\005C4.5.0\005Cweixin-java-pay-4.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cweixin-java-common\005C4.5.0\005Cweixin-java-common-4.5.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cthoughtworks\005Cxstream\005Cxstream\005C1.4.20\005Cxstream-1.4.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cgithub\005Cx-stream\005Cmxparser\005C1.2.2\005Cmxparser-1.2.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cxmlpull\005Cxmlpull\005C1.1.3.1\005Cxmlpull-1.1.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpmime\005C4.5.13\005Chttpmime-4.5.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cjcl-over-slf4j\005C1.7.31\005Cjcl-over-slf4j-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cqrcode-utils\005C1.1\005Cqrcode-utils-1.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Czxing\005Ccore\005C3.2.1\005Ccore-3.2.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cbouncycastle\005Cbcpkix-jdk15on\005C1.68\005Cbcpkix-jdk15on-1.68.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cbouncycastle\005Cbcprov-jdk15on\005C1.68\005Cbcprov-jdk15on-1.68.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgoogle\005Ccode\005Cgson\005Cgson\005C2.8.7\005Cgson-2.8.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cjavax\005Cservlet\005Cjavax.servlet-api\005C4.0.1\005Cjavax.servlet-api-4.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cbinarywang\005Cweixin-java-mp\005C4.4.0\005Cweixin-java-mp-4.4.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgooglecode\005Cmp4parser\005Cisoparser\005C1.1.22\005Cisoparser-1.1.22.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ccodehaus\005Cjackson\005Cjackson-mapper-asl\005C1.9.13\005Cjackson-mapper-asl-1.9.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Ccodehaus\005Cjackson\005Cjackson-core-asl\005C1.9.13\005Cjackson-core-asl-1.9.13.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cit\005Csauronsoftware\005Cjave\005C1.0.2\005Cjave-1.0.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cmicrosoft\005Ccognitiveservices\005Cspeech\005Cclient-sdk\005C1.40.0\005Cclient-sdk-1.40.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Chuaweicloud\005Csdk\005Chuaweicloud-sdk-sis\005C3.1.112\005Chuaweicloud-sdk-sis-3.1.112.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Chuaweicloud\005Csdk\005Chuaweicloud-sdk-core\005C3.1.112\005Chuaweicloud-sdk-core-3.1.112.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copeneuler\005Cbgmprovider\005C1.1.2\005Cbgmprovider-1.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copeneuler\005Cjca\005C1.1.2\005Cjca-1.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cbouncycastle\005Cbcprov-jdk18on\005C1.75\005Cbcprov-jdk18on-1.75.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Copeneuler\005Cjsse\005C1.1.2\005Cjsse-1.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Ctencentcloudapi\005Ctencentcloud-speech-sdk-java\005C1.0.48\005Ctencentcloud-speech-sdk-java-1.0.48.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccn\005Chutool\005Chutool-core\005C5.8.11\005Chutool-core-5.8.11.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccommons-codec\005Ccommons-codec\005C1.15\005Ccommons-codec-1.15.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokhttp3\005Cokhttp\005C3.14.9\005Cokhttp-3.14.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokhttp3\005Clogging-interceptor\005C3.14.9\005Clogging-interceptor-3.14.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpasyncclient\005C4.1.4\005Chttpasyncclient-4.1.4.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Capache\005Chttpcomponents\005Chttpcore-nio\005C4.4.14\005Chttpcore-nio-4.4.14.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cnet\005Cjodah\005Cexpiringmap\005C0.5.8\005Cexpiringmap-0.5.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Ccore\005Cjackson-annotations\005C2.12.3\005Cjackson-annotations-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cfasterxml\005Cjackson\005Ccore\005Cjackson-core\005C2.12.3\005Cjackson-core-2.12.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cdashscope-sdk-java\005C2.18.0\005Cdashscope-sdk-java-2.18.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Creactivex\005Crxjava2\005Crxjava\005C2.2.21\005Crxjava-2.2.21.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Creactivestreams\005Creactive-streams\005C1.0.3\005Creactive-streams-1.0.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cslf4j\005Cslf4j-simple\005C1.7.31\005Cslf4j-simple-1.7.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib-jdk8\005C1.5.20\005Ckotlin-stdlib-jdk8-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib\005C1.5.20\005Ckotlin-stdlib-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib-common\005C1.5.20\005Ckotlin-stdlib-common-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Ckotlin\005Ckotlin-stdlib-jdk7\005C1.5.20\005Ckotlin-stdlib-jdk7-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokio\005Cokio\005C3.6.0\005Cokio-3.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokio\005Cokio-jvm\005C3.6.0\005Cokio-jvm-3.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csquareup\005Cokhttp3\005Cokhttp-sse\005C3.14.9\005Cokhttp-sse-3.14.9.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgithub\005Cvictools\005Cjsonschema-generator\005C4.31.1\005Cjsonschema-generator-4.31.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Cbroadscope-bailian-sdk-java\005C1.3.0\005Cbroadscope-bailian-sdk-java-1.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Cbailian20230601\005C1.7.0\005Cbailian20230601-1.7.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea-util\005C0.2.21\005Ctea-util-0.2.21.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea-openapi\005C0.3.1\005Ctea-openapi-0.3.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ccredentials-java\005C0.3.0\005Ccredentials-java-0.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cxml\005Cbind\005Cjaxb-core\005C2.3.0\005Cjaxb-core-2.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Csun\005Cxml\005Cbind\005Cjaxb-impl\005C2.3.0\005Cjaxb-impl-2.3.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Calibabacloud-gateway-spi\005C0.0.1\005Calibabacloud-gateway-spi-0.0.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea-xml\005C0.1.5\005Ctea-xml-0.1.5.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Copenapiutil\005C0.2.1\005Copenapiutil-0.2.1.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Cendpoint-util\005C0.0.7\005Cendpoint-util-0.0.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Caliyun\005Ctea\005C1.2.7\005Ctea-1.2.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjetbrains\005Cannotations\005C23.0.0\005Cannotations-23.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cprojectreactor\005Creactor-core\005C3.4.7\005Creactor-core-3.4.7.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-all\005C4.1.25.Final\005Cnetty-all-4.1.25.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgl\005Cadmin\005Cadmin-core\005C1.6.0\005Cadmin-core-1.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cboot\005Cspring-boot-starter-data-redis\005C2.5.2\005Cspring-boot-starter-data-redis-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-redis\005C2.5.2\005Cspring-data-redis-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cdata\005Cspring-data-keyvalue\005C2.5.2\005Cspring-data-keyvalue-2.5.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-oxm\005C5.3.8\005Cspring-oxm-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cspring-context-support\005C5.3.8\005Cspring-context-support-5.3.8.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-boot-starter\005C3.0.0\005Cspringfox-boot-starter-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-oas\005C3.0.0\005Cspringfox-oas-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Ccore\005Cv3\005Cswagger-annotations\005C2.1.2\005Cswagger-annotations-2.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Ccore\005Cv3\005Cswagger-models\005C2.1.2\005Cswagger-models-2.1.2.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spi\005C3.0.0\005Cspringfox-spi-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-schema\005C3.0.0\005Cspringfox-schema-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-core\005C3.0.0\005Cspringfox-core-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spring-web\005C3.0.0\005Cspringfox-spring-web-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cgithub\005Cclassgraph\005Cclassgraph\005C4.8.83\005Cclassgraph-4.8.83.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spring-webmvc\005C3.0.0\005Cspringfox-spring-webmvc-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-spring-webflux\005C3.0.0\005Cspringfox-spring-webflux-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-swagger-common\005C3.0.0\005Cspringfox-swagger-common-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cmapstruct\005Cmapstruct\005C1.3.1.Final\005Cmapstruct-1.3.1.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-data-rest\005C3.0.0\005Cspringfox-data-rest-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-bean-validators\005C3.0.0\005Cspringfox-bean-validators-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-swagger2\005C3.0.0\005Cspringfox-swagger2-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Cswagger-annotations\005C1.5.20\005Cswagger-annotations-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cswagger\005Cswagger-models\005C1.5.20\005Cswagger-models-1.5.20.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cspringfox\005Cspringfox-swagger-ui\005C3.0.0\005Cspringfox-swagger-ui-3.0.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cplugin\005Cspring-plugin-core\005C2.0.0.RELEASE\005Cspring-plugin-core-2.0.0.RELEASE.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cspringframework\005Cplugin\005Cspring-plugin-metadata\005C2.0.0.RELEASE\005Cspring-plugin-metadata-2.0.0.RELEASE.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cfreemarker\005Cfreemarker\005C2.3.31\005Cfreemarker-2.3.31.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Corg\005Cjdom\005Cjdom\005C1.1.3\005Cjdom-1.1.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cgl\005Cadmin\005Cadmin-commons\005C1.6.0\005Cadmin-commons-1.6.0.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Cjamesmurty\005Cutils\005Cjava-xmlbuilder\005C1.3\005Cjava-xmlbuilder-1.3.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cnls\005Cnls-sdk-tts\005C2.1.6\005Cnls-sdk-tts-2.1.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Ccom\005Calibaba\005Cnls\005Cnls-sdk-common\005C2.1.6\005Cnls-sdk-common-2.1.6.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-transport\005C4.1.65.Final\005Cnetty-transport-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-resolver\005C4.1.65.Final\005Cnetty-resolver-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-handler\005C4.1.65.Final\005Cnetty-handler-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-common\005C4.1.65.Final\005Cnetty-common-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-codec-http\005C4.1.65.Final\005Cnetty-codec-http-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-codec\005C4.1.65.Final\005Cnetty-codec-4.1.65.Final.jar;C:\005CUsers\005CAdministrator\005C.m2\005Crepository\005Cio\005Cnetty\005Cnetty-buffer\005C4.1.65.Final\005Cnetty-buffer-4.1.65.Final.jar;


# Created at 2025-08-02T15:24:18.774
Unexpected IOException: I,1,sun.cpu.endian,little


# Created at 2025-08-02T15:24:18.775
Unexpected IOException: I,1,user.home,C:\005CUsers\005CAdministrator


# Created at 2025-08-02T15:24:18.785
Unexpected IOException: I,1,user.language,zh


# Created at 2025-08-02T15:24:18.787
Unexpected IOException: I,1,java.specification.vendor,Oracle Corporation


# Created at 2025-08-02T15:24:18.788
Unexpected IOException: I,1,java.home,C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre


# Created at 2025-08-02T15:24:18.789
Unexpected IOException: I,1,basedir,D:\005Cyny\005C4g\005Cadmin\005Cadmin-manager


# Created at 2025-08-02T15:24:18.790
Unexpected IOException: I,1,file.separator,\005C


# Created at 2025-08-02T15:24:18.805
Unexpected IOException: I,1,line.separator,\000D\000A


# Created at 2025-08-02T15:24:18.807
Unexpected IOException: I,1,jdk.vendor.version,Zulu 8.72.0.17-CA-win64


# Created at 2025-08-02T15:24:18.808
Unexpected IOException: I,1,java.vm.specification.vendor,Oracle Corporation


# Created at 2025-08-02T15:24:18.810
Unexpected IOException: I,1,java.specification.name,Java Platform API Specification


# Created at 2025-08-02T15:24:18.812
Unexpected IOException: I,1,java.awt.graphicsenv,sun.awt.Win32GraphicsEnvironment


# Created at 2025-08-02T15:24:18.815
Unexpected IOException: I,1,surefire.real.class.path,C:\005CUsers\005CADMINI~1\005CAppData\005CLocal\005CTemp\005Csurefire295056551933935664\005Csurefirebooter6908574142842254977.jar


# Created at 2025-08-02T15:24:18.816
Unexpected IOException: I,1,sun.boot.class.path,C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Cresources.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Crt.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Csunrsasign.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Cjsse.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Cjce.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Ccharsets.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Cjfr.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Ccat.jar;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Cclasses


# Created at 2025-08-02T15:24:18.817
Unexpected IOException: I,1,user.script,


# Created at 2025-08-02T15:24:18.818
Unexpected IOException: I,1,sun.management.compiler,HotSpot 64-Bit Tiered Compilers


# Created at 2025-08-02T15:24:18.820
Unexpected IOException: I,1,java.runtime.version,1.8.0_382-b05


# Created at 2025-08-02T15:24:18.821
Unexpected IOException: I,1,user.name,Administrator


# Created at 2025-08-02T15:24:18.822
Unexpected IOException: I,1,path.separator,;


# Created at 2025-08-02T15:24:18.822
Unexpected IOException: I,1,os.version,10.0


# Created at 2025-08-02T15:24:18.823
Unexpected IOException: I,1,java.endorsed.dirs,C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Cendorsed


# Created at 2025-08-02T15:24:18.824
Unexpected IOException: I,1,java.runtime.name,OpenJDK Runtime Environment


# Created at 2025-08-02T15:24:18.825
Unexpected IOException: I,1,file.encoding,UTF-8


# Created at 2025-08-02T15:24:18.826
Unexpected IOException: I,1,project.build.sourceEncoding,UTF-8


# Created at 2025-08-02T15:24:18.826
Unexpected IOException: I,1,java.vm.name,OpenJDK 64-Bit Server VM


# Created at 2025-08-02T15:24:18.827
Unexpected IOException: I,1,localRepository,C:\005CUsers\005CAdministrator\005C.m2\005Crepository


# Created at 2025-08-02T15:24:18.828
Unexpected IOException: I,1,java.vendor.url.bug,http://www.azul.com/support/


# Created at 2025-08-02T15:24:18.828
Unexpected IOException: I,1,java.io.tmpdir,C:\005CUsers\005CADMINI~1\005CAppData\005CLocal\005CTemp\005C


# Created at 2025-08-02T15:24:18.830
Unexpected IOException: I,1,java.version,1.8.0_382


# Created at 2025-08-02T15:24:18.831
Unexpected IOException: I,1,user.dir,D:\005Cyny\005C4g\005Cadmin\005Cadmin-manager


# Created at 2025-08-02T15:24:18.833
Unexpected IOException: I,1,os.arch,amd64


# Created at 2025-08-02T15:24:18.833
Unexpected IOException: I,1,java.vm.specification.name,Java Virtual Machine Specification


# Created at 2025-08-02T15:24:18.834
Unexpected IOException: I,1,java.awt.printerjob,sun.awt.windows.WPrinterJob


# Created at 2025-08-02T15:24:18.835
Unexpected IOException: I,1,project.reporting.outputEncoding,UTF-8


# Created at 2025-08-02T15:24:18.869
Unexpected IOException: I,1,sun.os.patch.level,


# Created at 2025-08-02T15:24:18.875
Unexpected IOException: I,1,java.library.path,C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Cbin;C:\005CWINDOWS\005CSun\005CJava\005Cbin;C:\005CWINDOWS\005Csystem32;C:\005CWINDOWS;D:\005CShadowBot;c:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005Ccursor\005Cresources\005Capp\005Cbin;D:\005C\865A\62DF\673A\005Cbin\005C;\005Csystem32;C:\005CWINDOWS;C:\005CWINDOWS\005CSystem32\005CWbem;C:\005CWINDOWS\005CSystem32\005CWindowsPowerShell\005Cv1.0\005C;C:\005CWINDOWS\005CSystem32\005COpenSSH\005C;C:\005CProgram Files\005CGit\005Ccmd;D:\005C\65B0\5EFA\6587\4EF6\5939\005CEasyShare\005Cx86\005C;D:\005C\65B0\5EFA\6587\4EF6\5939\005CEasyShare\005Cx64\005C;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cbin;D:\005Cffmpeg-6.1.1\005Cbin;C:\005CPython27;C:\005CWINDOWS\005Csystem32;D:\005C\5FAE\4FE1web\5F00\53D1\8005\5DE5\5177\005Cdll;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005Cpython\005CPython312;C:\005CProgram Files (x86)\005CTencent\005C\5FAE\4FE1web\5F00\53D1\8005\5DE5\5177\005Cdll;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CMicrosoft\005CWindowsApps;D:\005CIntelliJ IDEA 2022.3.3\005Cbin;C:\005CWindows\005CSystem32;D:\005Cmaven\005Capache-maven-3.6.3\005Cbin;D:\005Cadb\005Cadb;D:\005CDownloads\005Capache-tomcat-8.5.97\005Capache-tomcat-8.5.97\005Cbin;D:\005CPyCharm 2022.2.5\005Cbin;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005CPython\005CPython312;d:\005CDownloads\005Czh-fiddler-master\005Czh-fiddler-master;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cbin;D:\005Cffmpeg-6.1.1\005Cbin;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005CPython\005CPython312;C:\005CUsers\005CAdministrator\005CApp;C:\005CUsers\005CAdministrator\005CAppData\005CRoaming\005Cpython\005CPython312\005CScripts;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005Cnvm;C:\005Cnvm4w\005Cnodejs;D:\005CShadowBot;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CMicrosoft\005CWindowsApps;D:\005CIntelliJ IDEA 2022.3.3\005Cbin;D:\005CMicrosoft VS Code\005Cbin;C:\005CWindows\005CSystem32;D:\005Cmaven\005Capache-maven-3.6.3\005Cbin;D:\005Cadb\005Cadb;D:\005CDownloads\005Capache-tomcat-8.5.97\005Capache-tomcat-8.5.97\005Cbin;D:\005CPyCharm 2022.2.5\005Cbin;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005CPython\005CPython312;d:\005CDownloads\005Czh-fiddler-master\005Czh-fiddler-master;C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cbin;D:\005Cffmpeg-6.1.1\005Cbin;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005CPython\005CPython312;C:\005CUsers\005CAdministrator\005CAppData\005CRoaming\005Cnpm;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005Cpython\005CPython312;D:\005Canaconda3;D:\005Canaconda3\005CLibrary\005Cbin;D:\005Canaconda3\005CLibrary\005Cusr\005Cbin;D:\005Canaconda3\005CScripts;C:\005CUsers\005CAdministrator\005CAppData\005CRoaming\005Cpython\005CPython312\005CScripts;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005Ccursor\005Cresources\005Capp\005Cbin;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005Cnvm;C:\005Cnvm4w\005Cnodejs;C:\005CUsers\005CAdministrator\005CAppData\005CLocal\005CPrograms\005CMicrosoft VS Code\005Cbin;.


# Created at 2025-08-02T15:24:18.881
Unexpected IOException: I,1,java.vm.info,mixed mode


# Created at 2025-08-02T15:24:18.895
Unexpected IOException: I,1,java.vendor,Azul Systems\002C Inc.


# Created at 2025-08-02T15:24:18.896
Unexpected IOException: I,1,java.vm.version,25.382-b05


# Created at 2025-08-02T15:24:18.897
Unexpected IOException: I,1,java.specification.maintenance.version,5


# Created at 2025-08-02T15:24:18.897
Unexpected IOException: I,1,java.ext.dirs,C:\005CUsers\005CAdministrator\005C.jdks\005Cazul-1.8.0_382\005Cjre\005Clib\005Cext;C:\005CWINDOWS\005CSun\005CJava\005Clib\005Cext


# Created at 2025-08-02T15:24:18.899
Unexpected IOException: I,1,sun.io.unicode.encoding,UnicodeLittle


# Created at 2025-08-02T15:24:18.900
Unexpected IOException: I,1,java.class.version,52.0


# Created at 2025-08-02T15:24:19.881
Unexpected IOException: 1,1,org.apache.maven.surefire.junitplatform.JUnitPlatformProvider,com.gl.service.template.controller.TemplateControllerTest,null,null,null


# Created at 2025-08-02T15:24:19.956
Unexpected IOException with stream: SLF4J: Class path contains multiple SLF4J bindings.


# Created at 2025-08-02T15:24:19.967
Unexpected IOException with stream: SLF4J: Found binding in [jar:file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar!/org/slf4j/impl/StaticLoggerBinder.class]


# Created at 2025-08-02T15:24:19.997
Unexpected IOException with stream: SLF4J: Found binding in [jar:file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.31/slf4j-simple-1.7.31.jar!/org/slf4j/impl/StaticLoggerBinder.class]


# Created at 2025-08-02T15:24:20.023
Unexpected IOException with stream: SLF4J: See http://www.slf4j.org/codes.html#multiple_bindings for an explanation.


# Created at 2025-08-02T15:24:20.196
Unexpected IOException with stream: SLF4J: Actual binding is of type [ch.qos.logback.classic.util.ContextSelectorStaticBinder]


# Created at 2025-08-02T15:24:20.241
Unexpected IOException with stream: 15:24:20.208 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating CacheAwareContextLoaderDelegate from class [org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate]


# Created at 2025-08-02T15:24:20.277
Unexpected IOException with stream: 15:24:20.269 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating BootstrapContext using constructor [public org.springframework.test.context.support.DefaultBootstrapContext(java.lang.Class,org.springframework.test.context.CacheAwareContextLoaderDelegate)]


# Created at 2025-08-02T15:24:20.382
Unexpected IOException with stream: 15:24:20.382 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating TestContextBootstrapper for test class [com.gl.service.template.controller.TemplateControllerTest] from class [org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper]


# Created at 2025-08-02T15:24:20.400
Unexpected IOException with stream: 15:24:20.399 [main] INFO org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.gl.service.template.controller.TemplateControllerTest], using SpringBootContextLoader


# Created at 2025-08-02T15:24:20.407
Unexpected IOException with stream: 15:24:20.407 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.gl.service.template.controller.TemplateControllerTest]: class path resource [com/gl/service/template/controller/TemplateControllerTest-context.xml] does not exist


# Created at 2025-08-02T15:24:20.408
Unexpected IOException with stream: 15:24:20.408 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.gl.service.template.controller.TemplateControllerTest]: class path resource [com/gl/service/template/controller/TemplateControllerTestContext.groovy] does not exist


# Created at 2025-08-02T15:24:20.409
Unexpected IOException with stream: 15:24:20.409 [main] INFO org.springframework.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.gl.service.template.controller.TemplateControllerTest]: no resource found for suffixes {-context.xml, Context.groovy}.


# Created at 2025-08-02T15:24:20.411
Unexpected IOException with stream: 15:24:20.410 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.gl.service.template.controller.TemplateControllerTest]: TemplateControllerTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.


# Created at 2025-08-02T15:24:20.704
Unexpected IOException with stream: 15:24:20.703 [main] DEBUG org.springframework.test.context.support.ActiveProfilesUtils - Could not find an 'annotation declaring class' for annotation type [org.springframework.test.context.ActiveProfiles] and class [com.gl.service.template.controller.TemplateControllerTest]


# Created at 2025-08-02T15:24:20.872
Unexpected IOException with stream: 15:24:20.871 [main] DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider - Identified candidate component class: file [D:\yny\4g\admin\admin-manager\target\test-classes\com\gl\service\template\controller\TemplateTestApplication.class]


# Created at 2025-08-02T15:24:20.909
Unexpected IOException with stream: 15:24:20.906 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.gl.service.template.controller.TemplateTestApplication for test class com.gl.service.template.controller.TemplateControllerTest


# Created at 2025-08-02T15:24:20.967
Unexpected IOException with stream: 15:24:20.966 [main] DEBUG org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper - @TestExecutionListeners is not present for class [com.gl.service.template.controller.TemplateControllerTest]: using defaults.


# Created at 2025-08-02T15:24:20.968
Unexpected IOException with stream: 15:24:20.967 [main] INFO org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.security.test.context.support.WithSecurityContextTestExecutionListener, org.springframework.security.test.context.support.ReactorContextTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener]


# Created at 2025-08-02T15:24:21.087
Unexpected IOException with stream: 15:24:21.060 [main] INFO org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@51891008, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@2f953efd, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@f68f0dc, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@d2de489, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@14bdbc74, org.springframework.test.context.support.DirtiesContextTestExecutionListener@12591ac8, org.springframework.test.context.transaction.TransactionalTestExecutionListener@5a7fe64f, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@38145825, org.springframework.security.test.context.support.WithSecurityContextTestExecutionListener@41330d4f, org.springframework.test.context.event.EventPublishingTestExecutionListener@1b66c0fb, org.springframework.security.test.context.support.ReactorContextTestExecutionListener@3e0e1046, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@24c1b2d2, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@7dc19a70, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@508dec2b, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1e4f4a5c, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@37313c65, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@465232e9]


# Created at 2025-08-02T15:24:21.099
Unexpected IOException with stream: 15:24:21.098 [main] DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener - Before test class: context [DefaultTestContext@700fb871 testClass = TemplateControllerTest, testInstance = [null], testMethod = [null], testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@3b35a229 testClass = TemplateControllerTest, locations = '{}', classes = '{class com.gl.service.template.controller.TemplateTestApplication}', contextInitializerClasses = '[]', activeProfiles = '{}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper=true}', contextCustomizers = set[[ImportsContextCustomizer@9816741 key = [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration, org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration, org.springframework.boot.autoconfigure.data.web.SpringDataWebAutoConfiguration, org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration, org.springframework.boot.autoconfigure.groovy.template.GroovyTemplateAutoConfiguration, org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration, org.springframework.boot.autoconfigure.hateoas.HypermediaAutoConfiguration, org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration, org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration, org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration, org.springframework.boot.autoconfigure.mustache.MustacheAutoConfiguration, org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration, org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration, org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration, org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcAutoConfiguration, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcWebClientAutoConfiguration, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcWebDriverAutoConfiguration, org.springframework.boot.autoconfigure.security.oauth2.client.servlet.OAuth2ClientAutoConfiguration, org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration, org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration, org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration, org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcSecurityConfiguration, com.gl.service.template.controller.TemplateTestConfiguration]], org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@793be5ca, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@15aab8c6, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@8a12c18a, org.springframework.boot.test.autoconfigure.OverrideAutoConfigurationContextCustomizerFactory$DisableAutoConfigurationContextCustomizer@3d6f0054, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@25084a1e, org.springframework.boot.test.autoconfigure.filter.TypeExcludeFiltersContextCustomizer@e1e175d, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@11cb1a98, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@3e11f9e9, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map[[empty]]], class annotated with @DirtiesContext [false] with mode [null].


# Created at 2025-08-02T15:24:21.283
Unexpected IOException with stream: 15:24:21.241 [main] DEBUG org.springframework.test.context.support.TestPropertySourceUtils - Adding inlined properties to environment: {spring.jmx.enabled=false, org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTestContextBootstrapper=true}


# Created at 2025-08-02T15:24:22.539
Unexpected IOException with stream: Application Version: 1.0
Spring Boot Version: 2.5.2

+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
本系统版权属于广隆电子科技有限公司，未经许可，不能拷贝或者用于商业系统
如有问题请联系客服：
电话：13119577727
E-mail: <EMAIL>
+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++



# Created at 2025-08-02T15:24:22.612
Unexpected IOException with stream: 15:24:22.608 [34mINFO [0;39m [background-preinit] o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final


# Created at 2025-08-02T15:24:22.657
Unexpected IOException with stream: 15:24:22.656 [34mINFO [0;39m [main] c.g.s.t.c.TemplateControllerTest - Starting TemplateControllerTest using Java 1.8.0_382 on PC-20230417CHDY with PID 14920 (started by Administrator in D:\yny\4g\admin\admin-manager)


# Created at 2025-08-02T15:24:22.659
Unexpected IOException with stream: 15:24:22.658 [34mINFO [0;39m [main] c.g.s.t.c.TemplateControllerTest - The following profiles are active: dev


# Created at 2025-08-02T15:24:28.439
Unexpected IOException with stream: 15:24:28.438 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@4da39ca9' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)


# Created at 2025-08-02T15:24:28.518
Unexpected IOException with stream: 15:24:28.517 [34mINFO [0;39m [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)


# Created at 2025-08-02T15:24:34.445
Unexpected IOException with stream: 15:24:34.443 [31mWARN [0;39m [main] o.s.w.c.s.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig': Unsatisfied dependency expressed through field 'unauthorizedHandler'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gl.framework.security.handle.AuthenticationEntryPointImpl' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}


# Created at 2025-08-02T15:24:34.488
Unexpected IOException with stream: 15:24:34.487 [34mINFO [0;39m [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.


# Created at 2025-08-02T15:24:34.567
Unexpected IOException with stream: 15:24:34.566 [1;31mERROR[0;39m [main] o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field unauthorizedHandler in com.gl.framework.config.SecurityConfig required a bean of type 'com.gl.framework.security.handle.AuthenticationEntryPointImpl' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'com.gl.framework.security.handle.AuthenticationEntryPointImpl' in your configuration.



# Created at 2025-08-02T15:24:34.575
Unexpected IOException with stream: 15:24:34.573 [1;31mERROR[0;39m [main] o.s.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@d2de489] to prepare test instance [com.gl.service.template.controller.TemplateControllerTest@6f50d55c]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:132)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:124)
	at org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener.postProcessFields(MockitoTestExecutionListener.java:110)
	at org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener.injectFields(MockitoTestExecutionListener.java:94)
	at org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener.prepareTestInstance(MockitoTestExecutionListener.java:61)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:138)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:350)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:355)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$7(ClassBasedTestDescriptor.java:350)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:313)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:647)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:349)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$4(ClassBasedTestDescriptor.java:270)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:269)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:259)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:258)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:101)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:100)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:111)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:111)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:79)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig': Unsatisfied dependency expressed through field 'unauthorizedHandler'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gl.framework.security.handle.AuthenticationEntryPointImpl' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:660)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:434)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:123)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	... 68 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gl.framework.security.handle.AuthenticationEntryPointImpl' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1346)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1300)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	... 87 common frames omitted


# Created at 2025-08-02T15:24:34.616
Unexpected IOException: 5,1,com.gl.service.template.controller.TemplateControllerTest,testList_Success,null,null,null


# Created at 2025-08-02T15:24:34.631
Unexpected IOException: 7,1,com.gl.service.template.controller.TemplateControllerTest,testList_Success,null,Failed to load ApplicationContext,null,Failed to load ApplicationContext,TemplateControllerTest.testList_Success \00BB IllegalState Failed to load Applicat...,java.lang.IllegalStateException: Failed to load ApplicationContext\000ACaused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig': Unsatisfied dependency expressed through field 'unauthorizedHandler'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gl.framework.security.handle.AuthenticationEntryPointImpl' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}\000ACaused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.gl.framework.security.handle.AuthenticationEntryPointImpl' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}\000A


# Created at 2025-08-02T15:24:34.642
Unexpected IOException: 2,1,org.apache.maven.surefire.junitplatform.JUnitPlatformProvider,com.gl.service.template.controller.TemplateControllerTest,null,null,null


